# Home Assistant Entity Customizations
# This file contains friendly name overrides for better organization and usability

# === LIGHTS ===

# Bedroom Lights
light.habitacion_bombillo:
  friendly_name: "Bedroom Light Main"
  icon: mdi:lightbulb

# Kitchen Lights  
light.kitchen_light_left:
  friendly_name: "Kitchen Light Left"
light.kitchen_light_right:
  friendly_name: "Kitchen Light Right"
light.kitchen_light_center:
  friendly_name: "Kitchen Light Center"
light.kitchen_light_oven:
  friendly_name: "Kitchen Light Oven"
light.kitchen_led_strip:
  friendly_name: "Kitchen Strip Light"
light.kitchen_freezer_strip:
  friendly_name: "Kitchen Strip Light Freezer"

# Living Room Lights
light.living_light_left:
  friendly_name: "Living Room Light Left"
light.living_strip:
  friendly_name: "Living Room Strip Light"
light.living_neon:
  friendly_name: "Living Room Accent Light Main"
light.living_neon_small:
  friendly_name: "Living Room Accent Light Small"
light.living_lamp:
  friendly_name: "Living Room Lamp"

# Play Room Lights (formerly Games)
light.games_lamp:
  friendly_name: "Play Room Lamp"
light.games_lamp_light:
  friendly_name: "Play Room Lamp Light"

# Studio Lights
light.studio_light_l1:
  friendly_name: "Studio Light Main"

# Balcony Lights
light.balcon_bombillo:
  friendly_name: "Balcony Light Main"
light.balcon_bombillo_dos:
  friendly_name: "Balcony Light Secondary"
light.balcony_light_corner:
  friendly_name: "Balcony Light Corner"

# Bathroom Lights
light.bathroom_light_strip:
  friendly_name: "Bathroom Strip Light"

# Other Lights
light.humidifier:
  friendly_name: "Living Room Humidifier Light"

# === SWITCHES ===

# Kitchen Switches
switch.kitchen_freezer_switch:
  friendly_name: "Kitchen Switch Freezer"
switch.power_strip_switch_coffee_maker:
  friendly_name: "Kitchen Switch Coffee Maker"
switch.power_strip_switch_5:
  friendly_name: "Kitchen Switch Power Strip 5"
switch.power_strip_coffee_grinder:
  friendly_name: "Kitchen Switch Coffee Grinder"
switch.regleta_l3:
  friendly_name: "Kitchen Switch Power Strip"

# Studio Switches
switch.studio_music_switch:
  friendly_name: "Studio Switch Music"
switch.studio_server_switch:
  friendly_name: "Studio Switch Server"
switch.computador_linux:
  friendly_name: "Studio Switch Computer"

# Play Room Switches (formerly Games)
switch.games_rf_switch:
  friendly_name: "Play Room Switch RF"
switch.sofa_up:
  friendly_name: "Play Room Switch Sofa Up"
switch.sofa_down_2:
  friendly_name: "Play Room Switch Sofa Down"
switch.juegos_switch:
  friendly_name: "Play Room Switch Main"

# Bedroom Switches
switch.charger:
  friendly_name: "Bedroom Switch Charger"
switch.regleta_l2:
  friendly_name: "Bedroom Switch Power Strip"

# === FANS ===

fan.games_fan:
  friendly_name: "Play Room Fan"
fan.bedroom_fan:
  friendly_name: "Bedroom Fan Ceiling"
fan.bedroom_purifier:
  friendly_name: "Bedroom Air Purifier"
fan.games_purifier:
  friendly_name: "Play Room Air Purifier"
fan.studio_fan:
  friendly_name: "Studio Fan Ceiling"

# === MEDIA PLAYERS ===

media_player.bathroom_speaker:
  friendly_name: "Bathroom Speaker"
media_player.cuarto_2:
  friendly_name: "Bedroom Speaker"
media_player.casa_2:
  friendly_name: "Living Room Speaker"
media_player.speakers:
  friendly_name: "Living Room Speakers Main"

# === INPUT BOOLEANS ===

# Presence Detection
input_boolean.location_daniel_home:
  friendly_name: "Presence Daniel"
  icon: mdi:home-account

# Device Control Inputs
input_boolean.habitacion_bombilo_input:
  friendly_name: "Bedroom Light Main Input"
input_boolean.ventilador:
  friendly_name: "Play Room Fan Input"
input_boolean.ventilador_bed:
  friendly_name: "Bedroom Fan Input"

# Mode Controls
input_boolean.trabajo_switch:
  friendly_name: "Mode Work"
  icon: mdi:laptop
input_boolean.juegos_tv:
  friendly_name: "Play Room TV Mode"
  icon: mdi:television
input_boolean.sonido_5_1:
  friendly_name: "Audio Surround Mode"
  icon: mdi:surround-sound
input_boolean.is_pomodoro_running:
  friendly_name: "Timer Pomodoro Active"
  icon: mdi:timer
input_boolean.group_mode:
  friendly_name: "Mode Group"
input_boolean.mode_party:
  friendly_name: "Mode Party"
  icon: mdi:party-popper

# Window/Door Status (Spanish to English)
input_boolean.ventana:
  friendly_name: "Balcony Door Status"
  icon: mdi:door-open
input_boolean.ventana_habitacion_principal:
  friendly_name: "Bedroom Window Status"
  icon: mdi:window-open
input_boolean.ventana_juegos:
  friendly_name: "Play Room Window Status"
  icon: mdi:window-open
input_boolean.ventana_ropas:
  friendly_name: "Dressing Room Window Status"
  icon: mdi:window-open
input_boolean.puerta_principal:
  friendly_name: "Main Door Status"
  icon: mdi:door

# === SENSORS ===

# Presence Sensors
binary_sensor.living_presence_occupancy:
  friendly_name: "Living Room Presence Sensor"
  icon: mdi:motion-sensor
sensor.living_presence_illuminance:
  friendly_name: "Living Room Light Sensor"
  icon: mdi:brightness-6
sensor.living_presence_target_distance:
  friendly_name: "Living Room Distance Sensor"
  icon: mdi:ruler

# Window/Door Sensors
binary_sensor.juegos_sensor_ventana_contact:
  friendly_name: "Play Room Window Sensor"
  icon: mdi:window-open-variant
binary_sensor.bathroom_door_contact:
  friendly_name: "Bathroom Door Sensor"
  icon: mdi:door

# Device Sensors
sensor.pixel_9_pro_battery_level:
  friendly_name: "Phone Battery Level"
  icon: mdi:battery
sensor.washer:
  friendly_name: "Dressing Room Washer Status"
  icon: mdi:washing-machine

# Weather Sensors
sensor.forecast_temperature:
  friendly_name: "Weather Temperature Forecast"
  icon: mdi:thermometer

# === TIMERS ===

timer.pomodoro_focus_timer:
  friendly_name: "Timer Pomodoro Focus"
  icon: mdi:timer
timer.pomodoro_relax_timer:
  friendly_name: "Timer Pomodoro Break"
  icon: mdi:timer-pause
timer.shower_timer:
  friendly_name: "Timer Shower"
  icon: mdi:shower
timer.bathroom_use:
  friendly_name: "Timer Bathroom Use"
  icon: mdi:timer-outline

# === WEATHER ===

weather.forecast_home_2:
  friendly_name: "Weather Forecast Home"
  icon: mdi:weather-partly-cloudy

# === VACUUM ===

vacuum.robot:
  friendly_name: "Robot Vacuum"
  icon: mdi:robot-vacuum

# === REMOTES ===

remote.rf:
  friendly_name: "Play Room RF Remote"
  icon: mdi:remote
remote.ir:
  friendly_name: "Bedroom IR Remote"
  icon: mdi:remote
remote.cuarto:
  friendly_name: "Bedroom Remote"
  icon: mdi:remote

# === CAMERAS ===

camera.10_0_0_25:
  friendly_name: "Living Room Camera"
  icon: mdi:camera

# === DEVICE TRACKERS ===

device_tracker.pixel_9_pro:
  friendly_name: "Daniel Phone"
  icon: mdi:cellphone

# === BUTTONS (Physical) ===

# Kitchen Button
sensor.kitchen_button_action:
  friendly_name: "Kitchen Button"
  icon: mdi:gesture-tap-button

# Living Room Button
sensor.living_button_action:
  friendly_name: "Living Room Button"
  icon: mdi:gesture-tap-button

# Balcony Button
sensor.balcony_button_action:
  friendly_name: "Balcony Button"
  icon: mdi:gesture-tap-button

# Studio Button
sensor.studio_button_action:
  friendly_name: "Studio Button"
  icon: mdi:gesture-tap-button

# Play Room Button
sensor.games_button_action:
  friendly_name: "Play Room Button"
  icon: mdi:gesture-tap-button

# === CRYPTIC ENTITY IDS ===

# Technical Zigbee IDs that need friendly names
light.0xa4c138e94e11c12f:
  friendly_name: "Living Room Accent Light Technical"
  icon: mdi:lightbulb

switch.0x70b3d52b60014e3d_l2:
  friendly_name: "Bedroom Switch Power Strip L2"
  icon: mdi:power-socket-eu

switch.0x70b3d52b60014e3d_l4:
  friendly_name: "Bedroom Switch Power Strip L4"
  icon: mdi:power-socket-eu

# === ADDITIONAL SPANISH ENTITIES ===

# Entities that still have Spanish references
light.cocina_parrilla_bombillo:
  friendly_name: "Kitchen Light Grill"
  icon: mdi:lightbulb

switch.regleta_l5:
  friendly_name: "Kitchen Switch Power Strip L5"
  icon: mdi:power-socket-eu

# === ADDITIONAL SENSORS ===

# More specific sensor naming
sensor.living_presence_illuminance:
  friendly_name: "Living Room Light Level"
  icon: mdi:brightness-6

sensor.living_presence_target_distance:
  friendly_name: "Living Room Motion Distance"
  icon: mdi:ruler
