#!/usr/bin/env python3
"""
Comprehensive Entity Reference Checker
Identifies potential broken references after Zigbee device name changes.
"""

import re
import os

def search_file_for_patterns(file_path, patterns):
    """Search a file for specific patterns and return matches with line numbers."""
    matches = []
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
            for line_num, line in enumerate(lines, 1):
                for pattern_name, pattern in patterns.items():
                    if re.search(pattern, line, re.IGNORECASE):
                        matches.append({
                            'file': file_path,
                            'line': line_num,
                            'content': line.strip(),
                            'pattern': pattern_name
                        })
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return matches

def main():
    """Main function to check for broken references."""
    print("=== Checking for Broken Entity References ===\n")
    
    # Define problematic patterns to search for
    patterns = {
        'Spanish_juegos': r'\bjuegos_\w+',
        'Spanish_habitacion': r'\bhabitacion_\w+',
        'Spanish_cocina': r'\bcocina_\w+',
        'Spanish_regleta': r'\bregleta_\w+',
        'Spanish_ventana': r'\bventana_\w+',
        'Spanish_puerta': r'\bpuerta_\w+',
        'Old_games': r'\bgames_\w+',
        'Technical_hex': r'\b0x[a-fA-F0-9]{8,}\w*',
        'Zigbee_device_id': r'\b[a-fA-F0-9]{16}\b'
    }
    
    # Files to check
    files_to_check = ['automations.yaml', 'scripts.yaml']
    
    all_matches = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"Checking {file_path}...")
            matches = search_file_for_patterns(file_path, patterns)
            all_matches.extend(matches)
            print(f"  Found {len(matches)} potential issues")
        else:
            print(f"File not found: {file_path}")
    
    # Group matches by pattern type
    print(f"\n=== DETAILED RESULTS ===")
    
    pattern_groups = {}
    for match in all_matches:
        pattern = match['pattern']
        if pattern not in pattern_groups:
            pattern_groups[pattern] = []
        pattern_groups[pattern].append(match)
    
    if not pattern_groups:
        print("✅ No problematic entity references found!")
        return
    
    for pattern_name, matches in pattern_groups.items():
        print(f"\n🔍 {pattern_name} ({len(matches)} matches):")
        for match in matches:
            print(f"  📁 {match['file']}:{match['line']}")
            print(f"     {match['content']}")
    
    # Summary of critical issues
    print(f"\n=== CRITICAL ISSUES SUMMARY ===")
    
    critical_patterns = ['Spanish_juegos', 'Spanish_habitacion', 'Spanish_cocina', 'Spanish_regleta']
    critical_issues = []
    
    for pattern in critical_patterns:
        if pattern in pattern_groups:
            critical_issues.extend(pattern_groups[pattern])
    
    if critical_issues:
        print(f"⚠️  Found {len(critical_issues)} CRITICAL entity references that may break:")
        
        # Extract unique entity IDs
        entity_ids = set()
        for issue in critical_issues:
            # Extract entity IDs from the line
            entity_matches = re.findall(r'\b[a-z_]+\.[a-z0-9_]+\b', issue['content'])
            for entity in entity_matches:
                if any(spanish in entity for spanish in ['juegos', 'habitacion', 'cocina', 'regleta']):
                    entity_ids.add(entity)
        
        print(f"\n📋 Unique problematic entity IDs:")
        for entity_id in sorted(entity_ids):
            print(f"  - {entity_id}")
            
        print(f"\n💡 RECOMMENDATION:")
        print(f"   These entity IDs may need to be updated in automations/scripts")
        print(f"   OR verify that Home Assistant's entity registry will handle the mapping")
        
    else:
        print("✅ No critical Spanish entity references found in automations/scripts!")
    
    # Check for old naming patterns
    old_patterns = ['Old_games']
    old_issues = []
    
    for pattern in old_patterns:
        if pattern in pattern_groups:
            old_issues.extend(pattern_groups[pattern])
    
    if old_issues:
        print(f"\n📝 Found {len(old_issues)} references using old naming patterns:")
        old_entities = set()
        for issue in old_issues:
            entity_matches = re.findall(r'\b[a-z_]+\.[a-z0-9_]+\b', issue['content'])
            for entity in entity_matches:
                if 'games_' in entity:
                    old_entities.add(entity)
        
        for entity_id in sorted(old_entities):
            print(f"  - {entity_id}")

if __name__ == "__main__":
    main()
