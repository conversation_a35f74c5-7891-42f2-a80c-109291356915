#!/usr/bin/env python3
"""
Entity Reference Validation Script
Checks for potential broken entity references after renaming entities.
"""

import re
import os

def find_entity_references_in_text(text):
    """Find entity ID patterns in text using regex."""
    # Pattern to match entity IDs (domain.entity_name)
    pattern = r'\b[a-z_]+\.[a-z0-9_]+\b'
    matches = re.findall(pattern, text)
    
    # Filter to only include valid entity domains
    valid_domains = {
        'light', 'switch', 'sensor', 'binary_sensor', 'fan', 'media_player',
        'input_boolean', 'timer', 'automation', 'script', 'weather', 'vacuum',
        'remote', 'camera', 'device_tracker', 'climate', 'cover', 'lock'
    }
    
    return [match for match in matches if match.split('.')[0] in valid_domains]

def check_file_for_entities(file_path):
    """Check a file for entity references."""
    print(f"\nChecking {file_path}...")

    # Check raw text for entity patterns
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            text_entities = find_entity_references_in_text(content)
            print(f"  Found {len(text_entities)} entity patterns")
            return set(text_entities)
    except Exception as e:
        print(f"  Error reading file: {e}")
        return set()

def main():
    """Main validation function."""
    print("=== Home Assistant Entity Reference Validation ===\n")
    
    # Files to check
    config_files = [
        'configuration.yaml',
        'automations.yaml', 
        'scripts.yaml',
        'scenes.yaml',
        'customize.yaml'
    ]
    
    all_entities = set()
    
    # Check each configuration file
    for file_path in config_files:
        if os.path.exists(file_path):
            entities = check_file_for_entities(file_path)
            all_entities.update(entities)
        else:
            print(f"File not found: {file_path}")
    
    print(f"\n=== Summary ===")
    print(f"Total unique entity references found: {len(all_entities)}")
    
    # Group by domain
    domains = {}
    for entity in all_entities:
        if '.' in entity:
            domain = entity.split('.')[0]
            if domain not in domains:
                domains[domain] = []
            domains[domain].append(entity)
    
    print(f"\nEntities by domain:")
    for domain, entities in sorted(domains.items()):
        print(f"  {domain}: {len(entities)} entities")
    
    # Check for potential issues
    print(f"\n=== Potential Issues ===")
    
    # Look for Spanish entity names that might need updating
    spanish_patterns = ['habitacion', 'juegos', 'cocina', 'ventana', 'puerta', 'regleta']
    spanish_entities = []
    
    for entity in all_entities:
        for pattern in spanish_patterns:
            if pattern in entity.lower():
                spanish_entities.append(entity)
                break
    
    if spanish_entities:
        print(f"Entities with Spanish names that may need updating:")
        for entity in sorted(spanish_entities):
            print(f"  - {entity}")
    else:
        print("No obvious Spanish entity names found.")
    
    # Look for technical/cryptic entity IDs
    cryptic_entities = []
    for entity in all_entities:
        entity_name = entity.split('.')[1] if '.' in entity else entity
        # Check for hex patterns or very short/cryptic names
        if (re.search(r'[0-9a-f]{8,}', entity_name) or 
            len(entity_name) < 3 or 
            entity_name.startswith('0x')):
            cryptic_entities.append(entity)
    
    if cryptic_entities:
        print(f"\nCryptic/technical entity IDs found:")
        for entity in sorted(cryptic_entities):
            print(f"  - {entity}")
    else:
        print("\nNo obviously cryptic entity IDs found.")

if __name__ == "__main__":
    main()
