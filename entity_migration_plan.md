# Entity Migration Plan - CRITICAL SAFETY MEASURES

## ⚠️ CRITICAL ISSUE IDENTIFIED

The Zigbee2MQTT friendly name changes will likely cause **entity ID changes** that could break 60+ automation and script references.

## 🛡️ SAFER APPROACH RECOMMENDED

### Option 1: Use customize.yaml Only (SAFEST)
**Recommendation: Revert Zigbee2MQTT changes and use customize.yaml exclusively**

#### Why This is Safer:
- Entity IDs remain unchanged
- Only friendly names displayed to users change
- Zero risk of breaking automations
- Fully reversible

#### Implementation:
1. **Revert** `zigbee2mqtt/configuration.yaml` to original friendly names
2. **Keep** all customizations in `customize.yaml` 
3. **Add missing entities** to customize.yaml for complete coverage

### Option 2: Gradual Migration with Entity Registry Management
**If you want to proceed with Zigbee2MQTT changes:**

#### Phase 1: Backup and Preparation
1. **Full backup** of Home Assistant configuration
2. **Export entity registry** before changes
3. **Document all current entity IDs** used in automations

#### Phase 2: Entity ID Preservation
1. Use Home Assistant's entity registry to **manually set entity IDs** to match current ones
2. Apply Zigbee2MQTT friendly name changes
3. **Immediately** update entity registry to preserve old entity IDs

#### Phase 3: Validation and Testing
1. Restart Home Assistant
2. Verify all automations still work
3. Check entity registry for any unexpected changes

## 🔍 DETAILED IMPACT ANALYSIS

### Critical Entities That Will Break Automations:

#### Spanish Entity IDs (26 references):
```yaml
# Current → Potential New Entity ID
binary_sensor.juegos_sensor_ventana_contact → binary_sensor.play_room_window_sensor_contact
input_boolean.habitacion_bombilo_input → input_boolean.bedroom_light_main_input  
input_boolean.juegos_tv → input_boolean.play_room_tv_mode
switch.juegos_switch → switch.play_room_switch_main
switch.regleta_l2 → switch.bedroom_switch_power_strip_l2
switch.regleta_l3 → switch.kitchen_switch_power_strip_l3
switch.regleta_l5 → switch.kitchen_switch_power_strip_l5
```

#### "Games" Pattern Entities (21 references):
```yaml
# Current → Potential New Entity ID  
fan.games_fan → fan.play_room_fan
light.games_lamp → light.play_room_lamp
switch.games_rf_switch → switch.play_room_switch_rf
binary_sensor.games_presence_occupancy → binary_sensor.play_room_presence_occupancy
```

### Affected Automations (Partial List):
- Control - Leave - Energy Off
- Status - Window - Games - Closed/Opened  
- Control - Juegos - TV - ON/OFF
- Notify - ON Devices - Went OFF - Switch
- Control - Movie Mode - TV - ON/OFF
- And 15+ more automations

## 📋 IMMEDIATE ACTION REQUIRED

### Recommended Steps:

#### 1. **STOP** - Don't Apply Zigbee2MQTT Changes Yet
The current `zigbee2mqtt/configuration.yaml` changes should be **reverted** or **not applied** until entity ID preservation is confirmed.

#### 2. **Choose Safe Path** - Use customize.yaml Only
This provides all the user-facing benefits without the risks:

```yaml
# Add to customize.yaml instead:
binary_sensor.juegos_sensor_ventana_contact:
  friendly_name: "Play Room Window Sensor"
  
input_boolean.juegos_tv:
  friendly_name: "Play Room TV Mode"
  
switch.games_rf_switch:
  friendly_name: "Play Room Switch RF"
```

#### 3. **If Proceeding with Zigbee2MQTT Changes:**
- Create **full backup** first
- Test on **non-production** system if possible
- Have **rollback plan** ready
- Monitor entity registry changes closely

## 🎯 RECOMMENDED FINAL APPROACH

**Use the customize.yaml approach exclusively:**

1. ✅ **Keep** current `customize.yaml` with 80+ entity customizations
2. ❌ **Revert** `zigbee2mqtt/configuration.yaml` friendly name changes  
3. ✅ **Add missing entities** to customize.yaml for complete coverage
4. ✅ **Test** that friendly names appear correctly in UI
5. ✅ **Verify** all automations continue working

This approach provides:
- ✅ **100% safety** - no broken automations
- ✅ **User-friendly names** in the interface  
- ✅ **Easy maintenance** - all customizations in one file
- ✅ **Reversible** - can be undone anytime

## 🚨 CONCLUSION

**The Zigbee2MQTT friendly name changes pose a significant risk to system stability.** 

**Recommendation: Use customize.yaml exclusively for a safe, effective solution that achieves the same user experience goals without the automation breakage risk.**
