# Entity Renaming Implementation Plan

## Current Entity Analysis & Proposed Changes

### Lights

| Current Entity ID | Current Friendly Name | Proposed Friendly Name | Priority |
|-------------------|----------------------|------------------------|----------|
| `light.habitacion_bombillo` | "Habitacion - Bombillo" | "Bedroom Light Main" | High |
| `light.cocina_parrilla_bombillo` | (Spanish) | "Kitchen Light Oven" | High |
| `light.living_light_left` | "Living Light Left" | "Living Room Light Left" | Medium |
| `light.kitchen_light_left` | "Kitchen Light Left" | "Kitchen Light Left" | Low |
| `light.kitchen_light_right` | "Kitchen Light Right" | "Kitchen Light Right" | Low |
| `light.kitchen_light_center` | "Kitchen Light Center" | "Kitchen Light Center" | Low |
| `light.kitchen_light_oven` | "Kitchen Light Oven" | "Kitchen Light Oven" | Low |
| `light.kitchen_led_strip` | "Kitchen Led Strip" | "Kitchen Strip Light" | Medium |
| `light.kitchen_freezer_strip` | "Kitchen Freezer Strip" | "Kitchen Strip Light Freezer" | Medium |
| `light.living_strip` | "Living Strip" | "Living Room Strip Light" | Medium |
| `light.living_neon` | "Living Neon" | "Living Room Accent Light Main" | Medium |
| `light.living_neon_small` | "Living Neon Small" | "Living Room Accent Light Small" | Medium |
| `light.living_lamp` | "Living Lamp" | "Living Room Lamp" | Low |
| `light.games_lamp` | "Games Lamp" | "Play Room Lamp" | Medium |
| `light.games_lamp_light` | "Games Lamp Light" | "Play Room Lamp Light" | Medium |
| `light.studio_light_l1` | "Studio Light L1" | "Studio Light Main" | Medium |
| `light.balcon_bombillo` | (Spanish) | "Balcony Light Main" | High |
| `light.balcon_bombillo_dos` | (Spanish) | "Balcony Light Secondary" | High |
| `light.balcony_light_corner` | "Balcony Light Corner" | "Balcony Light Corner" | Low |
| `light.bathroom_light_strip` | "Bathroom Light Strip" | "Bathroom Strip Light" | Medium |
| `light.humidifier` | "Humidifier" | "Living Room Humidifier Light" | Medium |

### Switches

| Current Entity ID | Current Friendly Name | Proposed Friendly Name | Priority |
|-------------------|----------------------|------------------------|----------|
| `switch.kitchen_freezer_switch` | "Kitchen Freezer Switch" | "Kitchen Switch Freezer" | Low |
| `switch.power_strip_switch_coffee_maker` | "Power Strip Switch Coffee Maker" | "Kitchen Switch Coffee Maker" | Medium |
| `switch.power_strip_switch_5` | "Power Strip Switch 5" | "Kitchen Switch Power Strip 5" | High |
| `switch.power_strip_coffee_grinder` | "Power Strip Coffee Grinder" | "Kitchen Switch Coffee Grinder" | Medium |
| `switch.studio_music_switch` | "Studio Music Switch" | "Studio Switch Music" | Medium |
| `switch.studio_server_switch` | "Studio Server Switch" | "Studio Switch Server" | Medium |
| `switch.games_rf_switch` | "Games RF Switch" | "Play Room Switch RF" | Medium |
| `switch.sofa_up` | "Sofa Up" | "Play Room Switch Sofa Up" | Medium |
| `switch.sofa_down_2` | "Sofa Down 2" | "Play Room Switch Sofa Down" | Medium |
| `switch.charger` | "Charger" | "Bedroom Switch Charger" | Medium |
| `switch.regleta_l2` | (Spanish/Technical) | "Bedroom Switch Power Strip" | High |
| `switch.regleta_l3` | (Spanish/Technical) | "Kitchen Switch Power Strip" | High |
| `switch.juegos_switch` | (Spanish) | "Play Room Switch Main" | High |
| `switch.computador_linux` | (Spanish) | "Studio Switch Computer" | High |

### Fans

| Current Entity ID | Current Friendly Name | Proposed Friendly Name | Priority |
|-------------------|----------------------|------------------------|----------|
| `fan.games_fan` | "Games Fan" | "Play Room Fan" | Medium |
| `fan.bedroom_fan` | "Bedroom Fan" | "Bedroom Fan Ceiling" | Low |
| `fan.bedroom_purifier` | "Bedroom Purifier" | "Bedroom Air Purifier" | Medium |
| `fan.games_purifier` | "Games Purifier" | "Play Room Air Purifier" | Medium |
| `fan.studio_fan` | "Studio Fan" | "Studio Fan Ceiling" | Low |

### Media Players

| Current Entity ID | Current Friendly Name | Proposed Friendly Name | Priority |
|-------------------|----------------------|------------------------|----------|
| `media_player.bathroom_speaker` | "Bathroom Speaker" | "Bathroom Speaker" | Low |
| `media_player.cuarto_2` | (Spanish) | "Bedroom Speaker" | High |
| `media_player.casa_2` | (Spanish) | "Living Room Speaker" | High |
| `media_player.speakers` | "Speakers" | "Living Room Speakers Main" | Medium |

### Input Booleans

| Current Entity ID | Current Friendly Name | Proposed Friendly Name | Priority |
|-------------------|----------------------|------------------------|----------|
| `input_boolean.location_daniel_home` | "Location Daniel Home" | "Presence Daniel" | Medium |
| `input_boolean.habitacion_bombilo_input` | (Spanish) | "Bedroom Light Main Input" | High |
| `input_boolean.ventilador` | (Spanish) | "Play Room Fan Input" | High |
| `input_boolean.ventilador_bed` | (Spanish) | "Bedroom Fan Input" | High |
| `input_boolean.trabajo_switch` | (Spanish) | "Mode Work" | High |
| `input_boolean.juegos_tv` | (Spanish) | "Play Room TV Mode" | High |
| `input_boolean.sonido_5_1` | (Spanish) | "Audio Surround Mode" | High |
| `input_boolean.is_pomodoro_running` | "Is Pomodoro Running" | "Timer Pomodoro Active" | Medium |
| `input_boolean.group_mode` | "Group Mode" | "Mode Group" | Low |
| `input_boolean.mode_party` | "Mode Party" | "Mode Party" | Low |

### Sensors

| Current Entity ID | Current Friendly Name | Proposed Friendly Name | Priority |
|-------------------|----------------------|------------------------|----------|
| `binary_sensor.living_presence_occupancy` | "Living Presence Occupancy" | "Living Room Presence Sensor" | Medium |
| `binary_sensor.juegos_sensor_ventana_contact` | (Spanish) | "Play Room Window Sensor" | High |
| `binary_sensor.bathroom_door_contact` | "Bathroom Door Contact" | "Bathroom Door Sensor" | Low |
| `sensor.living_presence_illuminance` | "Living Presence Illuminance" | "Living Room Light Sensor" | Medium |
| `sensor.living_presence_target_distance` | "Living Presence Target Distance" | "Living Room Distance Sensor" | Medium |
| `sensor.pixel_9_pro_battery_level` | "Pixel 9 Pro Battery Level" | "Phone Battery Level" | Medium |
| `sensor.forecast_temperature` | "Forecast Temperature" | "Weather Temperature Forecast" | Low |
| `sensor.washer` | "Washer" | "Dressing Room Washer Status" | Medium |

### Window/Door Status Input Booleans

| Current Entity ID | Current Friendly Name | Proposed Friendly Name | Priority |
|-------------------|----------------------|------------------------|----------|
| `input_boolean.ventana` | (Spanish) | "Balcony Door Status" | High |
| `input_boolean.ventana_habitacion_principal` | (Spanish) | "Bedroom Window Status" | High |
| `input_boolean.ventana_juegos` | (Spanish) | "Play Room Window Status" | High |
| `input_boolean.ventana_ropas` | (Spanish) | "Dressing Room Window Status" | High |
| `input_boolean.puerta_principal` | (Spanish) | "Main Door Status" | High |

### Timers

| Current Entity ID | Current Friendly Name | Proposed Friendly Name | Priority |
|-------------------|----------------------|------------------------|----------|
| `timer.pomodoro_focus_timer` | "Pomodoro Focus Timer" | "Timer Pomodoro Focus" | Low |
| `timer.pomodoro_relax_timer` | "Pomodoro Relax Timer" | "Timer Pomodoro Break" | Low |
| `timer.shower_timer` | "Shower Timer" | "Timer Shower" | Low |
| `timer.bathroom_use` | "Bathroom Use" | "Timer Bathroom Use" | Low |

## Implementation Notes

### High Priority Items
- Spanish language entities (immediate user confusion)
- Technical/cryptic names (poor user experience)
- Inconsistent room naming (navigation issues)

### Medium Priority Items
- Standardizing device type naming
- Improving clarity of specific identifiers
- Consistent area assignments

### Low Priority Items
- Already well-named entities
- Minor formatting improvements
- Entities rarely seen by users

## Validation Requirements

Before implementing changes:
1. Backup current configuration
2. Test automation dependencies
3. Update dashboard references
4. Verify area assignments
5. Check script/automation references
