# Home Assistant Entity Naming Standards

## Overview
This document defines the consistent naming convention for all Home Assistant entities to improve organization, usability, and maintenance.

## Core Principles

### 1. Language Consistency
- **Primary Language**: English for all entity names
- **No Mixed Languages**: Avoid Spanish/English combinations
- **Clear Terminology**: Use standard Home Assistant terminology

### 2. Hierarchical Structure
**Format**: `[Room/Area] [Device Type] [Specific Identifier]`

Examples:
- `Living Room Light Main`
- `Kitchen Switch Coffee Maker`
- `Bedroom Fan Ceiling`

### 3. Room/Area Standardization

| Current Names | Standardized Name |
|---------------|-------------------|
| Habitacion, Bedroom | Bedroom |
| Juegos, Games, Play Room | Play Room |
| Cocina | Kitchen |
| Sala, Living | Living Room |
| Baño, Bathroom | Bathroom |
| Estudio | Studio |
| Balcon | Balcony |
| Ropas, Clothes Zone | Dressing Room |

## Device Type Categories

### Lighting
- **Main Light**: Primary room lighting
- **Accent Light**: Secondary/decorative lighting
- **Strip Light**: LED strips
- **Lamp**: Table/floor lamps
- **Bulb**: Individual smart bulbs

### Climate Control
- **Fan**: Ceiling or standalone fans
- **Purifier**: Air purifiers
- **Humidifier**: Humidity control devices

### Switches & Controls
- **Switch**: Power switches
- **Button**: Physical buttons/controls
- **Remote**: Remote controls

### Sensors & Monitoring
- **Presence Sensor**: Motion/occupancy detection
- **Door Sensor**: Door/window contacts
- **Temperature Sensor**: Climate monitoring

### Media & Entertainment
- **Speaker**: Audio devices
- **TV**: Television displays
- **Media Player**: Streaming devices

### Automation & Input
- **Input Boolean**: Virtual switches
- **Timer**: Countdown timers
- **Script**: Automation scripts

## Specific Identifier Guidelines

### When to Use Specific Identifiers
- Multiple similar devices in same room
- Different functions for same device type
- Positional references

### Examples
- `Kitchen Light Left` / `Kitchen Light Right`
- `Bedroom Fan Ceiling` / `Bedroom Fan Desk`
- `Living Room Switch Main` / `Living Room Switch Accent`

## Entity ID Conventions

### Format
`domain.room_devicetype_identifier`

### Examples
- `light.living_room_light_main`
- `switch.kitchen_switch_coffee_maker`
- `fan.bedroom_fan_ceiling`
- `binary_sensor.bathroom_door_sensor`

## Area Assignments

### Standard Areas
1. **Living Room** - Main living space
2. **Kitchen** - Cooking and dining area
3. **Bedroom** - Primary sleeping area
4. **Bathroom** - Main bathroom
5. **Play Room** - Gaming/entertainment room
6. **Studio** - Work/office space
7. **Balcony** - Outdoor balcony area
8. **Dressing Room** - Clothing storage area

## Special Cases

### Input Booleans
- Use descriptive names: `Location Daniel Home` → `Presence Daniel`
- Mode switches: `Work Mode` → `Mode Work`
- Status indicators: `Pomodoro Running` → `Timer Pomodoro Active`

### Media Players
- Include location and type: `Bathroom Speaker`, `Living Room TV`
- Avoid technical names: `cuarto_2` → `Bedroom Speaker`

### Automation Names
- Use action-based naming: `Control - Lights - Kitchen` → `Kitchen Lights Control`
- Include trigger type: `Status - Door - Opened` → `Door Status Monitor`
- Use descriptive action verbs: Control, Monitor, Alert, Schedule

## Implementation Priority

### Phase 1: Critical Entities
1. Lights (most visible to users)
2. Switches (frequently used)
3. Media players (daily interaction)

### Phase 2: Sensors & Monitoring
1. Presence sensors
2. Door/window sensors
3. Climate sensors

### Phase 3: Automation & Scripts
1. Input booleans
2. Timers
3. Scripts and automations

## Validation Checklist

- [ ] Entity name is in English only
- [ ] Follows Room + Device Type + Identifier format
- [ ] Uses standardized room names
- [ ] Device type is clear and descriptive
- [ ] Specific identifier is necessary and clear
- [ ] Entity ID follows snake_case convention
- [ ] Area assignment is correct
- [ ] No technical jargon or abbreviations
